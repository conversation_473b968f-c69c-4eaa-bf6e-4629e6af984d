#!/usr/bin/env python3
"""
Site Replication Performance Test Script
Tests the optimized site replication performance with the new configuration.
Target: Achieve 10000+ RPS with site replication enabled.
"""

import grpc
import asyncio
import time
import sys
import random
import string
import statistics
from typing import List, Tuple
import argparse

try:
    import rustycluster_pb2
    import rustycluster_pb2_grpc
except ImportError:
    print("Error: gRPC Python stubs not found.")
    print("Please generate them first with:")
    print("python -m grpc_tools.protoc -I. --python_out=. --grpc_python_out=. rustycluster.proto")
    sys.exit(1)

class SiteReplicationTester:
    def __init__(self, host="localhost", port=50051, username="testuser", password="testpass"):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.session_token = None
        self.channels = []
        self.stubs = []
        
    async def authenticate(self):
        """Authenticate and get session token"""
        if not self.username or not self.password:
            print("✓ No authentication required")
            return True
            
        channel = grpc.aio.insecure_channel(f'{self.host}:{self.port}')
        stub = rustycluster_pb2_grpc.KeyValueServiceStub(channel)
        
        auth_request = rustycluster_pb2.AuthenticateRequest(
            username=self.username,
            password=self.password
        )
        
        try:
            response = await stub.Authenticate(auth_request)
            if response.success:
                self.session_token = response.session_token
                print(f"✓ Authentication successful. Token: {self.session_token[:20]}...")
                await channel.close()
                return True
            else:
                print(f"✗ Authentication failed: {response.message}")
                await channel.close()
                return False
        except Exception as e:
            print(f"✗ Authentication error: {e}")
            await channel.close()
            return False
    
    async def create_connections(self, num_connections=50):
        """Create multiple gRPC connections for load testing"""
        print(f"Creating {num_connections} gRPC connections...")
        
        for i in range(num_connections):
            channel = grpc.aio.insecure_channel(f'{self.host}:{self.port}')
            stub = rustycluster_pb2_grpc.KeyValueServiceStub(channel)
            self.channels.append(channel)
            self.stubs.append(stub)
            
        print(f"✓ Created {len(self.stubs)} connections")
    
    def generate_test_data(self, count):
        """Generate test data optimized for site replication testing"""
        print(f"Generating {count} test data entries...")
        data = []
        for i in range(count):
            # Use consistent key patterns for better replication batching
            key = f"site_test_{i:08d}_{random.randint(1000, 9999)}"
            # Larger values to test site replication bandwidth
            value = f"site_replication_data_{i}_{''.join(random.choices(string.ascii_letters + string.digits, k=50))}"
            data.append((key, value))
        return data
    
    async def single_operation(self, stub, key, value, metadata, semaphore):
        """Perform a single Set operation optimized for site replication"""
        async with semaphore:
            start_time = time.time()
            try:
                request = rustycluster_pb2.SetRequest(
                    key=key,
                    value=value,
                    skip_replication=False,  # Ensure local replication happens
                    skip_site_replication=False  # Ensure site replication happens
                )
                
                response = await stub.Set(request, metadata=metadata, timeout=10.0)
                end_time = time.time()
                
                return {
                    'success': response.success,
                    'latency': (end_time - start_time) * 1000,
                    'error': None
                }
            except Exception as e:
                end_time = time.time()
                return {
                    'success': False,
                    'latency': (end_time - start_time) * 1000,
                    'error': str(e)
                }
    
    async def run_performance_test(self, target_rps=10000, duration_seconds=60, concurrency=100):
        """Run site replication performance test"""
        if not await self.authenticate():
            return False
            
        await self.create_connections(min(concurrency, 100))
        
        # Prepare metadata for authentication
        metadata = []
        if self.session_token:
            metadata = [('authorization', f'Bearer {self.session_token}')]
        
        # Generate test data
        total_operations = target_rps * duration_seconds
        test_data = self.generate_test_data(total_operations)
        
        print(f"\n🚀 Starting Site Replication Performance Test")
        print(f"Target RPS: {target_rps}")
        print(f"Duration: {duration_seconds} seconds")
        print(f"Total Operations: {total_operations}")
        print(f"Concurrency: {concurrency}")
        print(f"Site Replication: ENABLED")
        print("=" * 60)
        
        # Create semaphore for concurrency control
        semaphore = asyncio.Semaphore(concurrency)
        
        # Track results
        results = []
        start_time = time.time()
        
        # Calculate delay between operations to achieve target RPS
        delay_between_ops = 1.0 / target_rps if target_rps > 0 else 0
        
        # Create tasks for all operations
        tasks = []
        for i, (key, value) in enumerate(test_data):
            # Select stub in round-robin fashion
            stub = self.stubs[i % len(self.stubs)]
            
            # Schedule operation with appropriate delay
            operation_start_time = start_time + (i * delay_between_ops)
            
            task = asyncio.create_task(
                self.scheduled_operation(stub, key, value, metadata, semaphore, operation_start_time)
            )
            tasks.append(task)
        
        # Execute all operations
        print(f"Executing {len(tasks)} operations...")
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = time.time()
        actual_duration = end_time - start_time
        
        # Process results
        successful_ops = sum(1 for r in results if isinstance(r, dict) and r.get('success', False))
        failed_ops = len(results) - successful_ops
        
        latencies = [r['latency'] for r in results if isinstance(r, dict) and 'latency' in r]
        
        actual_rps = successful_ops / actual_duration if actual_duration > 0 else 0
        
        # Print results
        print("\n📊 Site Replication Performance Results")
        print("=" * 60)
        print(f"Actual Duration: {actual_duration:.2f} seconds")
        print(f"Successful Operations: {successful_ops:,}")
        print(f"Failed Operations: {failed_ops:,}")
        print(f"Actual RPS: {actual_rps:.2f}")
        print(f"Target Achievement: {(actual_rps/target_rps)*100:.1f}%")
        
        if latencies:
            print(f"\nLatency Statistics (ms):")
            print(f"  Average: {statistics.mean(latencies):.2f}")
            print(f"  Median: {statistics.median(latencies):.2f}")
            print(f"  P95: {sorted(latencies)[int(len(latencies)*0.95)]:.2f}")
            print(f"  P99: {sorted(latencies)[int(len(latencies)*0.99)]:.2f}")
            print(f"  Min: {min(latencies):.2f}")
            print(f"  Max: {max(latencies):.2f}")
        
        # Performance assessment
        print(f"\n🎯 Performance Assessment:")
        if actual_rps >= target_rps * 0.95:
            print(f"✅ EXCELLENT: Achieved {actual_rps:.0f} RPS (≥95% of target)")
        elif actual_rps >= target_rps * 0.80:
            print(f"✅ GOOD: Achieved {actual_rps:.0f} RPS (≥80% of target)")
        elif actual_rps >= target_rps * 0.60:
            print(f"⚠️  ACCEPTABLE: Achieved {actual_rps:.0f} RPS (≥60% of target)")
        else:
            print(f"❌ NEEDS IMPROVEMENT: Only achieved {actual_rps:.0f} RPS (<60% of target)")
        
        # Close connections
        for channel in self.channels:
            await channel.close()
        
        return actual_rps >= target_rps * 0.80  # Return success if ≥80% of target
    
    async def scheduled_operation(self, stub, key, value, metadata, semaphore, scheduled_time):
        """Execute operation at scheduled time"""
        # Wait until scheduled time
        current_time = time.time()
        if scheduled_time > current_time:
            await asyncio.sleep(scheduled_time - current_time)
        
        return await self.single_operation(stub, key, value, metadata, semaphore)

async def main():
    parser = argparse.ArgumentParser(description='Site Replication Performance Test')
    parser.add_argument('--host', default='localhost', help='RustyCluster host')
    parser.add_argument('--port', type=int, default=50051, help='RustyCluster port')
    parser.add_argument('--username', default='testuser', help='Username for authentication')
    parser.add_argument('--password', default='testpass', help='Password for authentication')
    parser.add_argument('--target-rps', type=int, default=10000, help='Target RPS')
    parser.add_argument('--duration', type=int, default=30, help='Test duration in seconds')
    parser.add_argument('--concurrency', type=int, default=100, help='Concurrent connections')
    
    args = parser.parse_args()
    
    tester = SiteReplicationTester(args.host, args.port, args.username, args.password)
    
    print("🔧 Site Replication Performance Tester")
    print(f"Target: {args.host}:{args.port}")
    print(f"Authentication: {args.username}/***** (enabled)")
    
    success = await tester.run_performance_test(
        target_rps=args.target_rps,
        duration_seconds=args.duration,
        concurrency=args.concurrency
    )
    
    if success:
        print("\n🎉 Performance test PASSED!")
        sys.exit(0)
    else:
        print("\n💥 Performance test FAILED!")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
