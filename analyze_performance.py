#!/usr/bin/env python3
"""
Simple performance analysis script to identify site replication bottlenecks.
This script runs quick tests to isolate the performance issue.
"""

import grpc
import asyncio
import time
import sys
import random
import string
import statistics
import argparse

try:
    import rustycluster_pb2
    import rustycluster_pb2_grpc
except ImportError:
    print("Error: gRPC Python stubs not found.")
    print("Please generate them first with:")
    print("python -m grpc_tools.protoc -I. --python_out=. --grpc_python_out=. rustycluster.proto")
    sys.exit(1)

class PerformanceAnalyzer:
    def __init__(self, host="localhost", port=50051, username="testuser", password="testpass"):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.session_token = None
        
    async def authenticate(self):
        """Authenticate and get session token"""
        if not self.username or not self.password:
            return True
            
        channel = grpc.aio.insecure_channel(f'{self.host}:{self.port}')
        stub = rustycluster_pb2_grpc.KeyValueServiceStub(channel)
        
        auth_request = rustycluster_pb2.AuthenticateRequest(
            username=self.username,
            password=self.password
        )
        
        try:
            response = await stub.Authenticate(auth_request)
            if response.success:
                self.session_token = response.session_token
                print(f"✓ Authentication successful")
                await channel.close()
                return True
            else:
                print(f"✗ Authentication failed: {response.message}")
                await channel.close()
                return False
        except Exception as e:
            print(f"✗ Authentication error: {e}")
            await channel.close()
            return False
    
    async def test_basic_performance(self, operations=1000, concurrency=10):
        """Test basic SET operation performance without site replication focus"""
        print(f"\n🔍 Testing Basic Performance")
        print(f"Operations: {operations}, Concurrency: {concurrency}")
        
        if not await self.authenticate():
            return False
        
        # Create connections
        channels = []
        stubs = []
        for i in range(concurrency):
            channel = grpc.aio.insecure_channel(f'{self.host}:{self.port}')
            stub = rustycluster_pb2_grpc.KeyValueServiceStub(channel)
            channels.append(channel)
            stubs.append(stub)
        
        # Prepare metadata
        metadata = []
        if self.session_token:
            metadata = [('authorization', f'Bearer {self.session_token}')]
        
        # Generate test data
        test_data = []
        for i in range(operations):
            key = f"perf_test_{i}_{random.randint(1000, 9999)}"
            value = f"test_value_{i}_{''.join(random.choices(string.ascii_letters, k=20))}"
            test_data.append((key, value))
        
        # Create semaphore for concurrency control
        semaphore = asyncio.Semaphore(concurrency)
        
        # Run test
        start_time = time.time()
        
        async def single_operation(stub, key, value):
            async with semaphore:
                op_start = time.time()
                try:
                    request = rustycluster_pb2.SetRequest(
                        key=key,
                        value=value,
                        skip_replication=False,  # Enable replication
                        skip_site_replication=False  # Enable site replication
                    )
                    response = await stub.Set(request, metadata=metadata, timeout=5.0)
                    op_end = time.time()
                    return {
                        'success': response.success,
                        'latency': (op_end - op_start) * 1000,
                        'error': None
                    }
                except Exception as e:
                    op_end = time.time()
                    return {
                        'success': False,
                        'latency': (op_end - op_start) * 1000,
                        'error': str(e)
                    }
        
        # Execute operations
        tasks = []
        for i, (key, value) in enumerate(test_data):
            stub = stubs[i % len(stubs)]
            task = single_operation(stub, key, value)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        # Analyze results
        duration = end_time - start_time
        successful = sum(1 for r in results if isinstance(r, dict) and r.get('success', False))
        failed = len(results) - successful
        
        latencies = [r['latency'] for r in results if isinstance(r, dict) and 'latency' in r]
        
        rps = successful / duration if duration > 0 else 0
        
        print(f"Duration: {duration:.2f}s")
        print(f"Successful: {successful}")
        print(f"Failed: {failed}")
        print(f"RPS: {rps:.2f}")
        
        if latencies:
            print(f"Latency - Avg: {statistics.mean(latencies):.2f}ms")
            print(f"Latency - P95: {sorted(latencies)[int(len(latencies)*0.95)]:.2f}ms")
        
        # Close connections
        for channel in channels:
            await channel.close()
        
        return rps
    
    async def test_batch_sizes(self):
        """Test different batch sizes to find optimal configuration"""
        print(f"\n🔍 Testing Different Load Levels")
        
        test_configs = [
            {"ops": 500, "conc": 10, "name": "Light Load"},
            {"ops": 1000, "conc": 20, "name": "Medium Load"},
            {"ops": 2000, "conc": 50, "name": "Heavy Load"},
        ]
        
        results = {}
        
        for config in test_configs:
            print(f"\n--- {config['name']} ---")
            rps = await self.test_basic_performance(config['ops'], config['conc'])
            results[config['name']] = rps
            
            # Wait between tests
            await asyncio.sleep(2)
        
        print(f"\n📊 Performance Summary:")
        for name, rps in results.items():
            print(f"{name}: {rps:.2f} RPS")
        
        return results
    
    async def test_connection_scaling(self):
        """Test how performance scales with connection count"""
        print(f"\n🔍 Testing Connection Scaling")
        
        connection_counts = [5, 10, 20, 50]
        operations = 1000
        
        results = {}
        
        for conn_count in connection_counts:
            print(f"\n--- {conn_count} Connections ---")
            rps = await self.test_basic_performance(operations, conn_count)
            results[conn_count] = rps
            
            # Wait between tests
            await asyncio.sleep(2)
        
        print(f"\n📊 Connection Scaling Results:")
        for conn_count, rps in results.items():
            print(f"{conn_count} connections: {rps:.2f} RPS")
        
        return results

async def main():
    parser = argparse.ArgumentParser(description='Performance Analysis Tool')
    parser.add_argument('--host', default='localhost', help='RustyCluster host')
    parser.add_argument('--port', type=int, default=50051, help='RustyCluster port')
    parser.add_argument('--username', default='testuser', help='Username for authentication')
    parser.add_argument('--password', default='testpass', help='Password for authentication')
    parser.add_argument('--test', choices=['basic', 'batch', 'scaling', 'all'], default='all', help='Test type to run')
    
    args = parser.parse_args()
    
    analyzer = PerformanceAnalyzer(args.host, args.port, args.username, args.password)
    
    print("🔧 RustyCluster Performance Analyzer")
    print(f"Target: {args.host}:{args.port}")
    print("=" * 50)
    
    if args.test in ['basic', 'all']:
        await analyzer.test_basic_performance(2000, 50)
    
    if args.test in ['batch', 'all']:
        await analyzer.test_batch_sizes()
    
    if args.test in ['scaling', 'all']:
        await analyzer.test_connection_scaling()
    
    print("\n✅ Analysis complete!")
    print("\n💡 Recommendations:")
    print("1. If RPS is significantly lower than expected, check:")
    print("   - Site replication node availability (port 50053, 50054)")
    print("   - Network latency between nodes")
    print("   - Resource utilization (CPU, memory)")
    print("2. If latency is high, consider:")
    print("   - Reducing batch_flush_interval_ms")
    print("   - Increasing connection pool sizes")
    print("   - Checking for lock contention")

if __name__ == "__main__":
    asyncio.run(main())
